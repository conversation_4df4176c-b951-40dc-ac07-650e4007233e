# Order Data Management and Enhanced SKU Extraction Guide

This guide covers the new order data management features and enhanced SKU extraction capabilities that have been implemented.

## 🚀 New Features

### 1. Order Data Clearing
- **Clear All Orders**: Delete all orders and order items from the database
- **Clear Platform-Specific Orders**: Delete orders from a specific platform (Etsy, Shopify)
- **Sync History Clearing**: Clear platform sync history and statistics

### 2. Enhanced SKU Extraction
- **Automatic Attribute Extraction**: Extract missing color, size, design, and style from SKU codes
- **Fallback Mechanism**: Use SKU extraction when platform metadata is incomplete
- **Multi-Source Data**: Combine data from platform APIs, metadata, properties, and SKU parsing

### 3. Comprehensive Sync Scripts
- **Complete Data Refresh**: Clear all data and re-sync from September 1st
- **Enhanced Data Quality**: Ensure all orders have complete product attributes
- **Error Handling**: Robust error handling and status reporting

## 📋 API Endpoints

### Order Data Clearing

#### Clear All Orders
```bash
DELETE /api/v1/platform/orders/clear
Authorization: Bearer <admin_token>
```

**Response:**
```json
{
  "success": true,
  "message": "All order data cleared successfully",
  "data": {
    "ordersDeleted": 150,
    "orderItemsDeleted": 300
  }
}
```

#### Clear Platform Orders
```bash
DELETE /api/v1/platform/orders/clear/{platform}
Authorization: Bearer <admin_token>
```

**Parameters:**
- `platform`: `etsy`, `shopify`, or `amazon`

**Response:**
```json
{
  "success": true,
  "message": "Order data for ETSY cleared successfully",
  "data": {
    "platform": "ETSY",
    "ordersDeleted": 75,
    "orderItemsDeleted": 150
  }
}
```

### Enhanced Order Sync

#### Sync Orders with Enhanced Extraction
```bash
POST /api/v1/platform/sync/orders/{platform}?startDate={start}&endDate={end}&limit={limit}&forceUpdate=true
Authorization: Bearer <admin_token>
```

**Parameters:**
- `platform`: `etsy` or `shopify`
- `startDate`: ISO date string (e.g., `2024-09-01T00:00:00Z`)
- `endDate`: ISO date string (e.g., `2024-12-12T23:59:59Z`)
- `limit`: Number of orders to process (default: 50)
- `forceUpdate`: Force update existing orders (default: false)

## 🛠️ Scripts

### Complete Data Refresh Script

Run the complete order data refresh:

```bash
./scripts/clear-and-resync-all-orders.sh
```

**What it does:**
1. Clears all existing order data
2. Clears sync history
3. Syncs Etsy orders from September 1st with enhanced SKU extraction
4. Syncs Shopify orders from September 1st with enhanced SKU extraction
5. Provides detailed progress reporting

### Test Script

Test the functionality before running the full sync:

```bash
./scripts/test-order-sync-flow.sh
```

**What it tests:**
1. API connectivity
2. Authentication
3. Order clearing endpoints
4. Platform status
5. Order sync endpoints
6. Data quality validation

## 🔧 Enhanced SKU Extraction

### How It Works

The enhanced SKU extraction system works as a fallback mechanism:

1. **Primary Data Sources**: Platform APIs provide order data (Etsy transactions, Shopify line items)
2. **Metadata Extraction**: Extract attributes from platform-specific metadata and properties
3. **SKU Fallback**: When color, size, design, or style is missing, extract from SKU code
4. **Title Parsing**: Additional extraction from product titles when needed

### SKU Patterns Supported

The system supports various SKU patterns:

- **Standard Format**: `G-CF6-S11-csfloral-GOLD`
- **Descriptive Format**: `ComfortFit-6mm-GreenEnchanted-12`
- **Design Prefixes**: `csfloral` (clear silicone), `gsfloral` (gold inlay), `ssfloral` (silver inlay)
- **Size Formats**: `S11`, `R11`, or plain numbers `11`

### Color Normalization

Colors are normalized and sorted by length for better matching:
- `RoseGold`/`RGold` → `RoseGold`
- `NL`/`NorthernLight` → `NorthernLight`
- `ef`/`enchanted` → `enchanted`

## 📊 Data Quality Improvements

### Before Enhancement
- Orders might be missing color, size, design, or style information
- Inconsistent data extraction across platforms
- Manual data entry required for missing attributes

### After Enhancement
- Automatic extraction of missing attributes from SKU codes
- Consistent data quality across all platforms
- Reduced manual intervention required
- Better inventory management and reporting

## 🚨 Important Notes

### Prerequisites
1. **Admin Access**: Order clearing requires admin role
2. **Platform Connections**: Ensure Etsy and Shopify are properly connected
3. **Database Backup**: Consider backing up data before clearing
4. **API Availability**: Ensure the API is running before executing scripts

### Safety Measures
- Order clearing operations cannot be undone
- Scripts include confirmation prompts and status checks
- Detailed logging and error reporting
- Graceful handling of API failures

### Performance Considerations
- Large date ranges may take significant time to process
- Rate limiting is respected for platform APIs
- Batch processing is used for large datasets
- Progress reporting helps monitor long-running operations

## 🔍 Monitoring and Troubleshooting

### Check Order Statistics
```bash
curl -X GET "http://localhost:3002/api/v1/orders/stats" \
  -H "Authorization: Bearer <token>"
```

### Check Platform Status
```bash
curl -X GET "http://localhost:3002/api/v1/platform/status/etsy" \
  -H "Authorization: Bearer <token>"
```

### View Recent Orders
```bash
curl -X GET "http://localhost:3002/api/v1/orders?limit=10" \
  -H "Authorization: Bearer <token>"
```

### Common Issues

1. **Authentication Errors**: Ensure admin user exists and credentials are correct
2. **Platform Connection Issues**: Check platform API credentials and connectivity
3. **Rate Limiting**: Scripts include delays to respect API rate limits
4. **Missing Data**: SKU extraction will fill in missing attributes automatically

## 📈 Next Steps

After running the sync:

1. **Verify Data Quality**: Check that orders have complete attribute information
2. **Monitor Performance**: Ensure the system handles the increased data load
3. **Update Reporting**: Take advantage of improved data quality in reports
4. **Schedule Regular Syncs**: Set up automated syncing for ongoing data freshness
