import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

import { User } from '@domain/user/user.entity';
import { UserRole } from '@domain/user/user-role.enum';
import { Email } from '@domain/user/value-objects/email.vo';
import { Password } from '@domain/user/value-objects/password.vo';
import { UserRepository } from '@infrastructure/database/repositories/user.repository';
import { CryptoService } from '@shared/services/crypto.service';
import { DateService } from '@shared/services/date.service';
import { EmailService } from '@infrastructure/email/email.service';

import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { VerifyEmailDto } from './dto/verify-email.dto';

export interface JwtPayload {
  sub: string;
  email: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

export interface AuthResult {
  user: Omit<User, 'password'>;
  accessToken: string;
  refreshToken?: string;
}

@Injectable()
export class AuthService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly jwtService: JwtService,
    private readonly cryptoService: CryptoService,
    private readonly dateService: DateService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Validate user credentials
   */
  async validateUser(email: string, password: string): Promise<User | null> {
    try {
      const emailVO = new Email(email);
      const user = await this.userRepository.findByEmail(emailVO.emailValue);

      if (!user || !user.isActive) {
        return null;
      }

      const passwordVO = user.getPasswordVO();
      const isPasswordValid = await passwordVO.compare(password);

      if (!isPasswordValid) {
        return null;
      }

      return user;
    } catch (error) {
      return null;
    }
  }

  /**
   * Login user
   */
  async login(loginDto: LoginDto): Promise<AuthResult> {
    const user = await this.validateUser(loginDto.email, loginDto.password);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (!user.isEmailVerified) {
      throw new UnauthorizedException('Please verify your email before logging in');
    }

    // Update last login
    user.updateLastLogin();
    await this.userRepository.save(user);

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload);

    return {
      user: user.toSafeJSON() as any,
      accessToken,
    };
  }

  /**
   * Register new user
   */
  async register(registerDto: RegisterDto): Promise<{ message: string }> {
    // Validate email
    const emailVO = new Email(registerDto.email);

    // Check if email already exists
    const existingUser = await this.userRepository.findByEmail(emailVO.emailValue);
    if (existingUser) {
      throw new ConflictException('Email already registered');
    }

    // Validate password
    const passwordVO = new Password(registerDto.password);
    const hashedPasswordVO = await passwordVO.hash();

    // Create user
    const user = this.userRepository.create({
      firstName: registerDto.firstName,
      lastName: registerDto.lastName,
      email: emailVO.emailValue,
      password: hashedPasswordVO.passwordValue,
      role: UserRole.USER,
      isActive: true,
      isEmailVerified: false,
    });

    // Generate email verification token (fixed for development)
    const verificationToken = '000-111';
    user.setEmailVerificationToken(verificationToken);

    await this.userRepository.save(user);

    // Skip email sending for development

    return {
      message: 'Registration successful. Please check your email to verify your account.',
    };
  }

  /**
   * Change password
   */
  async changePassword(
    userId: string,
    changePasswordDto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Verify current password
    const currentPasswordVO = user.getPasswordVO();
    const isCurrentPasswordValid = await currentPasswordVO.compare(
      changePasswordDto.currentPassword,
    );

    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Validate and hash new password
    const newPasswordVO = new Password(changePasswordDto.newPassword);
    const hashedNewPasswordVO = await newPasswordVO.hash();

    user.password = hashedNewPasswordVO.passwordValue;
    await this.userRepository.save(user);

    return {
      message: 'Password changed successfully',
    };
  }

  /**
   * Forgot password - send reset token
   */
  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    const emailVO = new Email(forgotPasswordDto.email);
    const user = await this.userRepository.findByEmail(emailVO.emailValue);

    if (!user || !user.isActive) {
      // Don't reveal if email exists for security
      return {
        message: 'If the email exists, a password reset link has been sent.',
      };
    }

    // Generate reset token (fixed for development)
    const resetToken = '000-111';
    const expirationMinutes = this.configService.get<number>(
      'PASSWORD_RESET_EXPIRATION_MINUTES',
      60,
    );

    user.setPasswordResetToken(resetToken, expirationMinutes);
    await this.userRepository.save(user);

    // Skip email sending for development

    return {
      message: 'If the email exists, a password reset link has been sent.',
    };
  }

  /**
   * Reset password with token
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    const user = await this.userRepository.findByResetPasswordToken(resetPasswordDto.token);

    if (!user || !user.isPasswordResetValid()) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Validate and hash new password
    const newPasswordVO = new Password(resetPasswordDto.newPassword);
    const hashedNewPasswordVO = await newPasswordVO.hash();

    user.password = hashedNewPasswordVO.passwordValue;
    user.clearPasswordResetToken();

    await this.userRepository.save(user);

    return {
      message: 'Password reset successfully',
    };
  }

  /**
   * Verify email with token
   */
  async verifyEmail(verifyEmailDto: VerifyEmailDto): Promise<{ message: string }> {
    const user = await this.userRepository.findByEmailVerificationToken(verifyEmailDto.token);

    if (!user) {
      throw new BadRequestException('Invalid verification token');
    }

    user.verifyEmail();
    await this.userRepository.save(user);

    // Skip welcome email for development

    return {
      message: 'Email verified successfully',
    };
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(email: string): Promise<{ message: string }> {
    const emailVO = new Email(email);
    const user = await this.userRepository.findByEmail(emailVO.emailValue);

    if (!user || !user.isActive) {
      return {
        message: 'If the email exists, a verification link has been sent.',
      };
    }

    if (user.isEmailVerified) {
      throw new BadRequestException('Email is already verified');
    }

    // Generate new verification token (fixed for development)
    const verificationToken = '000-111';
    user.setEmailVerificationToken(verificationToken);

    await this.userRepository.save(user);

    // Skip email sending for development

    return {
      message: 'If the email exists, a verification link has been sent.',
    };
  }

  /**
   * Get user profile
   */
  async getProfile(userId: string): Promise<Omit<User, 'password'>> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    return user.toSafeJSON() as any;
  }

  /**
   * Update user profile
   */
  async updateProfile(
    userId: string,
    updateData: { firstName?: string; lastName?: string; avatar?: string },
  ): Promise<Omit<User, 'password'>> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    user.updateProfile(updateData);
    await this.userRepository.save(user);

    return user.toSafeJSON() as any;
  }

  /**
   * Refresh access token
   */
  async refreshToken(userId: string): Promise<{ accessToken: string }> {
    const user = await this.userRepository.findById(userId);
    if (!user || !user.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload);

    return { accessToken };
  }

  /**
   * Logout user (invalidate token - would need Redis blacklist in production)
   */
  async logout(userId: string): Promise<{ message: string }> {
    // In a production environment, you would add the token to a blacklist in Redis
    // For now, we'll just return a success message
    return {
      message: 'Logged out successfully',
    };
  }
}
