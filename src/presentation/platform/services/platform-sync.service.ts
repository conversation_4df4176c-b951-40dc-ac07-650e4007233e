import { Injectable, Logger } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { ProductRepository } from '@infrastructure/database/repositories/product.repository';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { RedisService } from '@infrastructure/redis/redis.service';

export interface SyncStatistics {
  totalProducts: number;
  totalOrders: number;
  lastSyncTimes: Record<PlatformSource, Date | null>;
  syncCounts: Record<PlatformSource, number>;
}

export interface SyncHistoryEntry {
  platform: PlatformSource;
  timestamp: Date;
  success: boolean;
  productsProcessed: number;
  ordersProcessed: number;
  duration: number;
  errors: string[];
}

@Injectable()
export class PlatformSyncService {
  private readonly logger = new Logger(PlatformSyncService.name);
  private readonly SYNC_HISTORY_KEY = 'platform:sync:history';
  private readonly SYNC_STATS_KEY = 'platform:sync:stats';
  private readonly LAST_SYNC_KEY = 'platform:sync:last';

  constructor(
    private readonly productRepository: ProductRepository,
    private readonly orderRepository: OrderRepository,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Record sync result
   */
  async recordSyncResult(result: {
    platform: PlatformSource;
    success: boolean;
    productsProcessed: number;
    ordersProcessed: number;
    duration: number;
    errors: string[];
  }): Promise<void> {
    const entry: SyncHistoryEntry = {
      ...result,
      timestamp: new Date(),
    };

    try {
      // Add to sync history (keep last 100 entries per platform)
      const historyKey = `${this.SYNC_HISTORY_KEY}:${result.platform}`;
      await this.redisService.lpush(historyKey, JSON.stringify(entry));
      
      // Trim to keep only last 100 entries
      const listLength = await this.redisService.llen(historyKey);
      if (listLength > 100) {
        await this.redisService.ltrim(historyKey, 0, 99);
      }

      // Update last sync time
      const lastSyncKey = `${this.LAST_SYNC_KEY}:${result.platform}`;
      await this.redisService.set(lastSyncKey, entry.timestamp.toISOString());

      // Update sync count
      const syncCountKey = `${this.SYNC_STATS_KEY}:count:${result.platform}`;
      await this.redisService.incr(syncCountKey);

      this.logger.debug(`Recorded sync result for ${result.platform}`, entry);
    } catch (error) {
      this.logger.error(`Failed to record sync result for ${result.platform}:`, error);
    }
  }

  /**
   * Get sync statistics
   */
  async getSyncStatistics(): Promise<SyncStatistics> {
    try {
      // Get total products and orders from database
      const [totalProducts, totalOrders] = await Promise.all([
        this.productRepository.count(),
        this.orderRepository.count(),
      ]);

      // Get last sync times for each platform
      const platforms = Object.values(PlatformSource);
      const lastSyncPromises = platforms.map(async (platform) => {
        const lastSyncKey = `${this.LAST_SYNC_KEY}:${platform}`;
        const lastSyncStr = await this.redisService.get<string>(lastSyncKey);
        return {
          platform,
          lastSync: lastSyncStr ? new Date(lastSyncStr) : null,
        };
      });

      const lastSyncResults = await Promise.all(lastSyncPromises);
      const lastSyncTimes = lastSyncResults.reduce((acc, { platform, lastSync }) => {
        acc[platform] = lastSync;
        return acc;
      }, {} as Record<PlatformSource, Date | null>);

      // Get sync counts for each platform
      const syncCountPromises = platforms.map(async (platform) => {
        const syncCountKey = `${this.SYNC_STATS_KEY}:count:${platform}`;
        const count = await this.redisService.get<number>(syncCountKey);
        return {
          platform,
          count: count || 0,
        };
      });

      const syncCountResults = await Promise.all(syncCountPromises);
      const syncCounts = syncCountResults.reduce((acc, { platform, count }) => {
        acc[platform] = count;
        return acc;
      }, {} as Record<PlatformSource, number>);

      return {
        totalProducts,
        totalOrders,
        lastSyncTimes,
        syncCounts,
      };
    } catch (error) {
      this.logger.error('Failed to get sync statistics:', error);
      
      // Return default values on error
      const platforms = Object.values(PlatformSource);
      return {
        totalProducts: 0,
        totalOrders: 0,
        lastSyncTimes: platforms.reduce((acc, platform) => {
          acc[platform] = null;
          return acc;
        }, {} as Record<PlatformSource, Date | null>),
        syncCounts: platforms.reduce((acc, platform) => {
          acc[platform] = 0;
          return acc;
        }, {} as Record<PlatformSource, number>),
      };
    }
  }

  /**
   * Get sync history
   */
  async getSyncHistory(
    platform?: PlatformSource,
    limit: number = 50,
  ): Promise<SyncHistoryEntry[]> {
    try {
      if (platform) {
        // Get history for specific platform
        const historyKey = `${this.SYNC_HISTORY_KEY}:${platform}`;
        const entries = await this.redisService.lrange(historyKey, 0, limit - 1);
        
        return entries.map(entry => {
          try {
            return JSON.parse(entry);
          } catch (parseError) {
            this.logger.warn(`Failed to parse sync history entry: ${entry}`);
            return null;
          }
        }).filter(Boolean);
      } else {
        // Get history for all platforms
        const platforms = Object.values(PlatformSource);
        const historyPromises = platforms.map(async (plt) => {
          const historyKey = `${this.SYNC_HISTORY_KEY}:${plt}`;
          const entries = await this.redisService.lrange(historyKey, 0, Math.ceil(limit / platforms.length) - 1);
          
          return entries.map(entry => {
            try {
              return JSON.parse(entry);
            } catch (parseError) {
              this.logger.warn(`Failed to parse sync history entry: ${entry}`);
              return null;
            }
          }).filter(Boolean);
        });

        const allHistories = await Promise.all(historyPromises);
        const combinedHistory = allHistories.flat();

        // Sort by timestamp (newest first) and limit
        return combinedHistory
          .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
          .slice(0, limit);
      }
    } catch (error) {
      this.logger.error(`Failed to get sync history for ${platform || 'all platforms'}:`, error);
      return [];
    }
  }

  /**
   * Clear sync history for platform
   */
  async clearSyncHistory(platform: PlatformSource): Promise<void> {
    try {
      const historyKey = `${this.SYNC_HISTORY_KEY}:${platform}`;
      await this.redisService.del(historyKey);
      
      this.logger.log(`Cleared sync history for ${platform}`);
    } catch (error) {
      this.logger.error(`Failed to clear sync history for ${platform}:`, error);
    }
  }

  /**
   * Clear all sync data
   */
  async clearAllSyncData(): Promise<void> {
    try {
      const platforms = Object.values(PlatformSource);
      const keysToDelete: string[] = [];

      // Collect all keys to delete
      for (const platform of platforms) {
        keysToDelete.push(
          `${this.SYNC_HISTORY_KEY}:${platform}`,
          `${this.LAST_SYNC_KEY}:${platform}`,
          `${this.SYNC_STATS_KEY}:count:${platform}`,
        );
      }

      // Delete all keys
      if (keysToDelete.length > 0) {
        await this.redisService.delMany(keysToDelete);
      }

      this.logger.log('Cleared all sync data');
    } catch (error) {
      this.logger.error('Failed to clear all sync data:', error);
    }
  }

  /**
   * Get platform sync status
   */
  async getPlatformSyncStatus(platform: PlatformSource): Promise<{
    lastSync: Date | null;
    syncCount: number;
    lastResult: SyncHistoryEntry | null;
  }> {
    try {
      const [lastSyncStr, syncCount, historyEntries] = await Promise.all([
        this.redisService.get<string>(`${this.LAST_SYNC_KEY}:${platform}`),
        this.redisService.get<number>(`${this.SYNC_STATS_KEY}:count:${platform}`),
        this.getSyncHistory(platform, 1),
      ]);

      return {
        lastSync: lastSyncStr ? new Date(lastSyncStr) : null,
        syncCount: syncCount || 0,
        lastResult: historyEntries.length > 0 ? historyEntries[0] : null,
      };
    } catch (error) {
      this.logger.error(`Failed to get sync status for ${platform}:`, error);
      return {
        lastSync: null,
        syncCount: 0,
        lastResult: null,
      };
    }
  }

  /**
   * Check if platform needs sync (based on last sync time)
   */
  async isPlatformSyncNeeded(
    platform: PlatformSource,
    maxAgeMinutes: number = 60,
  ): Promise<boolean> {
    try {
      const lastSyncKey = `${this.LAST_SYNC_KEY}:${platform}`;
      const lastSyncStr = await this.redisService.get<string>(lastSyncKey);

      if (!lastSyncStr) {
        return true; // Never synced
      }

      const lastSync = new Date(lastSyncStr);
      const now = new Date();
      const ageMinutes = (now.getTime() - lastSync.getTime()) / (1000 * 60);

      return ageMinutes >= maxAgeMinutes;
    } catch (error) {
      this.logger.error(`Failed to check if sync needed for ${platform}:`, error);
      return true; // Assume sync is needed on error
    }
  }

  /**
   * Get platforms that need sync
   */
  async getPlatformsNeedingSync(maxAgeMinutes: number = 60): Promise<PlatformSource[]> {
    const platforms = Object.values(PlatformSource);
    const needsSyncPromises = platforms.map(async (platform) => ({
      platform,
      needsSync: await this.isPlatformSyncNeeded(platform, maxAgeMinutes),
    }));

    const results = await Promise.all(needsSyncPromises);
    return results.filter(result => result.needsSync).map(result => result.platform);
  }
}
