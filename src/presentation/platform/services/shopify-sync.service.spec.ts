import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { ShopifySyncService } from './shopify-sync.service';
import { ShopifyApiService } from '@infrastructure/external/shopify/shopify-api.service';
import { ProductRepository } from '@infrastructure/database/repositories/product.repository';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import { DateService } from '@shared/services/date.service';
import { PlatformSource } from '@domain/product/platform-source.enum';

describe('ShopifySyncService', () => {
  let service: ShopifySyncService;
  let shopifyApiService: jest.Mocked<ShopifyApiService>;
  let productRepository: jest.Mocked<ProductRepository>;
  let orderRepository: jest.Mocked<OrderRepository>;
  let orderItemRepository: jest.Mocked<OrderItemRepository>;
  let dateService: jest.Mocked<DateService>;

  beforeEach(async () => {
    const mockShopifyApiService = {
      getProducts: jest.fn(),
      getOrders: jest.fn(),
      healthCheck: jest.fn(),
    };

    const mockProductRepository = {
      findBySku: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockOrderRepository = {
      findByExternalId: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockOrderItemRepository = {
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockDateService = {
      subDays: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ShopifySyncService,
        { provide: ShopifyApiService, useValue: mockShopifyApiService },
        { provide: ProductRepository, useValue: mockProductRepository },
        { provide: OrderRepository, useValue: mockOrderRepository },
        { provide: OrderItemRepository, useValue: mockOrderItemRepository },
        { provide: DateService, useValue: mockDateService },
      ],
    }).compile();

    service = module.get<ShopifySyncService>(ShopifySyncService);
    shopifyApiService = module.get(ShopifyApiService);
    productRepository = module.get(ProductRepository);
    orderRepository = module.get(OrderRepository);
    orderItemRepository = module.get(OrderItemRepository);
    dateService = module.get(DateService);

    // Mock logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
    jest.spyOn(Logger.prototype, 'debug').mockImplementation();
  });

  describe('processOrder with null values', () => {
    it('should handle order with null id gracefully', async () => {
      const mockOrder = {
        id: null,
        admin_graphql_api_id: 'gid://shopify/Order/null',
        app_id: null,
        browser_ip: null,
        buyer_accepts_marketing: false,
        cancel_reason: null,
        cancelled_at: null,
        cart_token: null,
        checkout_id: null,
        checkout_token: null,
        client_details: null,
        closed_at: null,
        confirmed: true,
        contact_email: null,
        created_at: '2024-01-01T00:00:00Z',
        currency: 'USD',
        current_subtotal_price: '100.00',
        current_subtotal_price_set: {},
        current_total_discounts: '0.00',
        current_total_discounts_set: {},
        current_total_duties_set: null,
        current_total_price: '110.00',
        current_total_price_set: {},
        current_total_tax: '10.00',
        current_total_tax_set: {},
        customer_locale: null,
        device_id: null,
        discount_codes: [],
        email: '<EMAIL>',
        estimated_taxes: false,
        financial_status: 'paid',
        fulfillment_status: 'fulfilled',
        gateway: 'shopify_payments',
        landing_site: null,
        landing_site_ref: null,
        location_id: null,
        name: 'Test Order',
        note: null,
        note_attributes: [],
        number: 1001,
        order_number: 1001,
        order_status_url: null,
        original_total_duties_set: null,
        payment_gateway_names: ['shopify_payments'],
        phone: null,
        presentment_currency: 'USD',
        processed_at: '2024-01-01T00:00:00Z',
        processing_method: 'direct',
        reference: null,
        referring_site: null,
        source_identifier: null,
        source_name: 'web',
        source_url: null,
        subtotal_price: '100.00',
        subtotal_price_set: {},
        tags: '',
        tax_lines: [],
        taxes_included: false,
        test: false,
        token: 'test-token',
        total_discounts: '0.00',
        total_discounts_set: {},
        total_line_items_price: '100.00',
        total_line_items_price_set: {},
        total_outstanding: '0.00',
        total_price: '110.00',
        total_price_set: {},
        total_price_usd: '110.00',
        total_shipping_price_set: {},
        total_tax: '10.00',
        total_tax_set: {},
        total_tip_received: '0.00',
        total_weight: 100,
        updated_at: '2024-01-01T00:00:00Z',
        user_id: null,
        billing_address: null,
        customer: null,
        discount_applications: [],
        fulfillments: [],
        line_items: [],
        payment_terms: null,
        refunds: [],
        shipping_address: null,
        shipping_lines: [],
      };

      orderRepository.findByExternalId.mockResolvedValue(null);
      orderRepository.create.mockReturnValue({} as any);
      orderRepository.save.mockResolvedValue({} as any);

      // Should not throw an error
      await expect(service['processOrder'](mockOrder)).resolves.not.toThrow();

      expect(orderRepository.findByExternalId).toHaveBeenCalledWith('', PlatformSource.SHOPIFY);
      expect(orderRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          externalOrderId: '',
          orderNumber: 'Test Order',
          customerEmail: '<EMAIL>',
          customerPhone: '',
          customerName: '',
        }),
      );
    });

    it('should handle line items with null product_id and variant_id', async () => {
      const mockLineItem = {
        id: 123,
        admin_graphql_api_id: 'gid://shopify/LineItem/123',
        fulfillable_quantity: 1,
        fulfillment_service: 'manual',
        fulfillment_status: null,
        gift_card: false,
        grams: 100,
        name: 'Test Product',
        origin_location: null,
        price: '50.00',
        price_set: {},
        product_exists: true,
        product_id: null,
        properties: [],
        quantity: 1,
        requires_shipping: true,
        sku: 'TEST-SKU',
        taxable: true,
        title: 'Test Product',
        total_discount: '0.00',
        total_discount_set: {},
        variant_id: null,
        variant_inventory_management: 'shopify',
        variant_title: 'Default Title',
        vendor: 'Test Vendor',
        tax_lines: [],
        duties: [],
        discount_allocations: [],
      };

      orderItemRepository.create.mockReturnValue({} as any);
      orderItemRepository.save.mockResolvedValue({} as any);

      // Should not throw an error
      await expect(service['createOrderItem']('order-id', mockLineItem)).resolves.not.toThrow();

      expect(orderItemRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          sku: 'TEST-SKU',
          title: 'Test Product',
          externalProductId: '',
          externalVariantId: '',
        }),
      );
    });

    it('should handle product variant with null inventory_item_id', async () => {
      const mockProduct = {
        id: 123,
        title: 'Test Product',
        body_html: '<p>Test description</p>',
        vendor: 'Test Vendor',
        product_type: 'Test Type',
        created_at: '2024-01-01T00:00:00Z',
        handle: 'test-product',
        updated_at: '2024-01-01T00:00:00Z',
        published_at: '2024-01-01T00:00:00Z',
        status: 'active',
        published_scope: 'web',
        tags: 'test',
        admin_graphql_api_id: 'gid://shopify/Product/123',
        variants: [
          {
            id: 456,
            product_id: 123,
            title: 'Default Title',
            price: '50.00',
            sku: 'TEST-SKU',
            position: 1,
            inventory_policy: 'deny',
            compare_at_price: null,
            fulfillment_service: 'manual',
            inventory_management: 'shopify',
            option1: null,
            option2: null,
            option3: null,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
            taxable: true,
            barcode: null,
            grams: 100,
            image_id: null,
            weight: 0.1,
            weight_unit: 'kg',
            inventory_item_id: null,
            inventory_quantity: null,
            old_inventory_quantity: 0,
            requires_shipping: true,
            admin_graphql_api_id: 'gid://shopify/ProductVariant/456',
          },
        ],
        options: [],
        images: [],
      };

      productRepository.findBySku.mockResolvedValue(null);
      productRepository.create.mockReturnValue({} as any);
      productRepository.save.mockResolvedValue({} as any);

      // Should not throw an error
      await expect(service['processProduct'](mockProduct)).resolves.not.toThrow();

      expect(productRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          sku: 'TEST-SKU',
          inventoryItemId: '',
          shopifyQuantity: 0,
        }),
      );
    });
  });

  describe('healthCheck', () => {
    it('should return true when Shopify API is healthy', async () => {
      shopifyApiService.healthCheck.mockResolvedValue(true);

      const result = await service.healthCheck();

      expect(result).toBe(true);
      expect(shopifyApiService.healthCheck).toHaveBeenCalled();
    });

    it('should return false when Shopify API is unhealthy', async () => {
      shopifyApiService.healthCheck.mockRejectedValue(new Error('API Error'));

      const result = await service.healthCheck();

      expect(result).toBe(false);
      expect(shopifyApiService.healthCheck).toHaveBeenCalled();
    });
  });
});
