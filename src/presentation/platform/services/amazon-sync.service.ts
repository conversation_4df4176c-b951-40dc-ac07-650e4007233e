import { Injectable, Logger } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { ProductStatus } from '@domain/product/product-status.enum';
import { OrderStatus } from '@domain/order/order-status.enum';
import { ProductRepository } from '@infrastructure/database/repositories/product.repository';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import {
  AmazonApiService,
  AmazonOrder,
  AmazonOrderItem,
  AmazonInventoryItem,
} from '@infrastructure/external/amazon/amazon-api.service';
// import { PlatformSyncService, SyncHistoryEntry } from './platform-sync.service'; // temporarily disabled
import { DateService } from '@shared/services/date.service';
import { ProductAttributeExtractor } from '@shared/utils/product-attribute-extractor.util';

export interface AmazonSyncResult {
  platform: PlatformSource.AMAZON;
  success: boolean;
  productsProcessed: number;
  ordersProcessed: number;
  errors: string[];
  duration: number;
  timestamp: Date;
}

@Injectable()
export class AmazonSyncService {
  private readonly logger = new Logger(AmazonSyncService.name);

  constructor(
    private readonly amazonApiService: AmazonApiService,
    private readonly productRepository: ProductRepository,
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
    // private readonly platformSyncService: PlatformSyncService, // temporarily disabled
    private readonly dateService: DateService,
  ) {}

  /**
   * Sync all Amazon data (products and orders)
   */
  async syncAll(): Promise<AmazonSyncResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let productsProcessed = 0;
    let ordersProcessed = 0;

    try {
      this.logger.log('Starting Amazon sync (products and orders)');

      // Sync products
      try {
        productsProcessed = await this.syncProducts();
      } catch (error) {
        this.logger.error('Failed to sync Amazon products:', error);
        errors.push(`Product sync failed: ${error.message}`);
      }

      // Sync orders
      try {
        ordersProcessed = await this.syncOrders();
      } catch (error) {
        this.logger.error('Failed to sync Amazon orders:', error);
        errors.push(`Order sync failed: ${error.message}`);
      }

      const result: AmazonSyncResult = {
        platform: PlatformSource.AMAZON,
        success: errors.length === 0,
        productsProcessed,
        ordersProcessed,
        errors,
        duration: Date.now() - startTime,
        timestamp: new Date(),
      };

      // Record sync result
      // await this.platformSyncService.recordSyncResult(result); // temporarily disabled

      return result;
    } catch (error) {
      this.logger.error('Amazon sync failed:', error);

      const result: AmazonSyncResult = {
        platform: PlatformSource.AMAZON,
        success: false,
        productsProcessed,
        ordersProcessed,
        errors: [...errors, error.message],
        duration: Date.now() - startTime,
        timestamp: new Date(),
      };

      // await this.platformSyncService.recordSyncResult(result); // temporarily disabled
      return result;
    }
  }

  /**
   * Sync Amazon products/inventory
   */
  async syncProducts(): Promise<number> {
    this.logger.log('Starting Amazon product sync');
    let processed = 0;

    try {
      let nextToken: string | undefined;

      do {
        // Get inventory summary from Amazon
        const inventoryResponse = await this.amazonApiService.getInventorySummary({
          details: true,
          nextToken,
          maxResultsPerPage: 50,
        });

        const inventoryItems = inventoryResponse.inventoryItems;
        nextToken = inventoryResponse.nextToken;

        for (const item of inventoryItems) {
          try {
            await this.processInventoryItem(item);
            processed++;
          } catch (error) {
            this.logger.error(`Failed to process inventory item ${item.sellerSku}:`, error);
          }
        }

        // Add delay to respect rate limits
        if (nextToken) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } while (nextToken);

      this.logger.log(`Processed ${processed} Amazon inventory items`);
      return processed;
    } catch (error) {
      this.logger.error('Failed to sync Amazon products:', error);
      throw error;
    }
  }

  /**
   * Sync Amazon orders
   */
  async syncOrders(): Promise<number> {
    this.logger.log('Starting Amazon order sync');
    let processed = 0;

    try {
      // Get orders from last 30 days
      const thirtyDaysAgo = this.dateService.subDays(new Date(), 30);
      let nextToken: string | undefined;

      do {
        // Get orders from Amazon
        const ordersResponse = await this.amazonApiService.getOrders({
          createdAfter: thirtyDaysAgo,
          maxResultsPerPage: 50,
          nextToken,
        });

        const orders = ordersResponse.orders;
        nextToken = ordersResponse.nextToken;

        for (const order of orders) {
          try {
            await this.processOrder(order);
            processed++;
          } catch (error) {
            this.logger.error(`Failed to process order ${order.orderId}:`, error);
          }
        }

        // Add delay to respect rate limits
        if (nextToken) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } while (nextToken);

      this.logger.log(`Processed ${processed} Amazon orders`);
      return processed;
    } catch (error) {
      this.logger.error('Failed to sync Amazon orders:', error);
      throw error;
    }
  }

  /**
   * Process inventory item from Amazon
   */
  private async processInventoryItem(item: AmazonInventoryItem): Promise<void> {
    // Check if product exists
    let product = await this.productRepository.findBySku(item.sellerSku);

    if (!product) {
      // Create new product
      product = this.productRepository.create({
        sku: item.sellerSku,
        externalProductId: item.asin,
        fnsku: item.fnSku,
        asin: item.asin,
        title: item.productName || item.sellerSku,
        status: ProductStatus.ACTIVE,
        source: PlatformSource.AMAZON,
        quantity: 0,
        amazonQuantity: item.fulfillableQuantity,
        shopifyQuantity: 0,
        etsyQuantity: 0,
        currency: 'USD',
        externalCreatedAt: new Date(),
      });
    } else {
      // Update existing product
      product.updateAmazonInfo({
        asin: item.asin,
        fnsku: item.fnSku,
        quantity: item.fulfillableQuantity,
      });
    }

    await this.productRepository.save(product);
  }

  /**
   * Process order from Amazon
   */
  private async processOrder(amazonOrder: AmazonOrder): Promise<void> {
    // Check if order already exists
    const existingOrder = await this.orderRepository.findByExternalId(
      amazonOrder.orderId,
      PlatformSource.AMAZON,
    );

    if (existingOrder) {
      // Update existing order if needed
      this.updateExistingOrder(existingOrder, amazonOrder);
      await this.orderRepository.save(existingOrder);
      return;
    }

    // Get order items
    const orderItems = await this.amazonApiService.getOrderItems(amazonOrder.orderId);

    // Create new order
    const order = this.orderRepository.create({
      externalOrderId: amazonOrder.orderId,
      orderNumber: amazonOrder.orderId,
      status: this.mapAmazonOrderStatus(amazonOrder.orderStatus),
      source: PlatformSource.AMAZON,
      externalCreatedAt: amazonOrder.purchaseDate,
      customerEmail: '', // Amazon doesn't provide email in order data
      subtotalPrice: amazonOrder.orderTotal.amount,
      shippingPrice: 0,
      taxAmount: 0,
      discountAmount: 0,
      totalPrice: amazonOrder.orderTotal.amount,
      currency: amazonOrder.orderTotal.currencyCode,
      fulfillmentChannel: amazonOrder.fulfillmentChannel,
      salesChannel: amazonOrder.salesChannel,
      isProcessed: false,
      metadata: {
        amazonData: {
          marketplaceId: amazonOrder.marketplaceId,
          isPrime: amazonOrder.isPrime,
          isBusinessOrder: amazonOrder.isBusinessOrder,
          shipmentServiceLevelCategory: amazonOrder.shipmentServiceLevelCategory,
        },
      },
    });

    const savedOrder = await this.orderRepository.save(order);

    // Create order items
    for (const item of orderItems) {
      await this.createOrderItem(savedOrder.id, item);
    }
  }

  /**
   * Create order item from Amazon order item
   */
  private async createOrderItem(orderId: string, amazonItem: AmazonOrderItem): Promise<void> {
    // Extract product attributes
    const attributes = ProductAttributeExtractor.extractAttributes({
      title: amazonItem.title,
      description: amazonItem.productInfo?.title || '',
      sku: amazonItem.sellerSku,
      metadata: {
        asin: amazonItem.asin,
        orderItemId: amazonItem.orderItemId,
        conditionId: amazonItem.conditionId,
        isGift: amazonItem.isGift,
        productInfo: amazonItem.productInfo,
      },
    });

    const orderItem = this.orderItemRepository.create({
      orderId,
      sku: amazonItem.sellerSku,
      title: amazonItem.title,
      style: attributes.style,
      color: attributes.color,
      design: attributes.design,
      size: attributes.size,
      quantity: amazonItem.quantityOrdered,
      unitPrice: amazonItem.itemPrice?.amount || 0,
      totalPrice: (amazonItem.itemPrice?.amount || 0) * amazonItem.quantityOrdered,
      currency: amazonItem.itemPrice?.currencyCode || 'USD',
      externalProductId: amazonItem.asin,
      isCustom: attributes.isCustom,
      isEngrave: attributes.isEngrave,
      customization: attributes.customization,
      engraving: attributes.engraving,
      metadata: {
        amazonData: {
          orderItemId: amazonItem.orderItemId,
          quantityShipped: amazonItem.quantityShipped,
          conditionId: amazonItem.conditionId,
          isGift: amazonItem.isGift,
        },
        extractedAttributes: attributes,
      },
    });

    await this.orderItemRepository.save(orderItem);
  }

  /**
   * Update existing order with Amazon data
   */
  private updateExistingOrder(existingOrder: any, amazonOrder: AmazonOrder): void {
    const newStatus = this.mapAmazonOrderStatus(amazonOrder.orderStatus);

    if (existingOrder.status !== newStatus) {
      existingOrder.updateStatus(newStatus);
    }

    // Update metadata
    if (!existingOrder.metadata) {
      existingOrder.metadata = {};
    }
    existingOrder.metadata.amazonData = {
      ...existingOrder.metadata.amazonData,
      lastUpdated: amazonOrder.lastUpdateDate,
      orderStatus: amazonOrder.orderStatus,
    };
  }

  /**
   * Map Amazon order status to our order status
   */
  private mapAmazonOrderStatus(amazonStatus: string): OrderStatus {
    switch (amazonStatus.toLowerCase()) {
      case 'pending':
        return OrderStatus.PENDING;
      case 'unshipped':
        return OrderStatus.CONFIRMED;
      case 'partiallyshipped':
        return OrderStatus.PROCESSING;
      case 'shipped':
        return OrderStatus.SHIPPED;
      case 'cancelled':
        return OrderStatus.CANCELLED;
      case 'unfulfillable':
        return OrderStatus.CANCELLED;
      default:
        return OrderStatus.PENDING;
    }
  }

  /**
   * Update inventory for a specific SKU
   */
  async updateInventory(sku: string, quantity: number): Promise<boolean> {
    try {
      this.logger.log(`Updating Amazon inventory for SKU ${sku} to quantity ${quantity}`);

      const success = await this.amazonApiService.updateInventoryQuantity(sku, quantity);

      if (success) {
        // Update local product record
        const product = await this.productRepository.findBySku(sku);
        if (product) {
          product.updateQuantityForPlatform(PlatformSource.AMAZON, quantity);
          await this.productRepository.save(product);
        }
      }

      return success;
    } catch (error) {
      this.logger.error(`Failed to update Amazon inventory for SKU ${sku}:`, error);
      return false;
    }
  }

  /**
   * Health check for Amazon API
   */
  async healthCheck(): Promise<boolean> {
    try {
      return await this.amazonApiService.healthCheck();
    } catch (error) {
      this.logger.error('Amazon health check failed:', error);
      return false;
    }
  }

  /**
   * Get Amazon-specific sync statistics
   */
  async getAmazonSyncStats(): Promise<{
    totalProducts: number;
    totalOrders: number;
    lastSync: Date | null;
  }> {
    const [totalProducts, totalOrders, syncStatus] = await Promise.all([
      this.productRepository.count({ source: PlatformSource.AMAZON }),
      this.orderRepository.count({ source: PlatformSource.AMAZON }),
      // this.platformSyncService.getPlatformSyncStatus(PlatformSource.AMAZON), // temporarily disabled
      Promise.resolve({ lastSync: null, syncCount: 0, isHealthy: true }),
    ]);

    return {
      totalProducts,
      totalOrders,
      lastSync: syncStatus.lastSync,
    };
  }

  /**
   * Sync orders with date filters and duplicate prevention
   */
  async syncOrdersWithFilters(params: {
    startDate?: string;
    endDate?: string;
    limit: number;
    forceUpdate: boolean;
  }): Promise<{
    ordersProcessed: number;
    ordersCreated: number;
    ordersUpdated: number;
    ordersSkipped: number;
  }> {
    this.logger.log('Starting Amazon order sync with filters', params);

    let ordersProcessed = 0;
    let ordersCreated = 0;
    let ordersUpdated = 0;
    let ordersSkipped = 0;

    try {
      // Build Amazon API parameters
      const apiParams: any = {
        MaxResultsPerPage: Math.min(params.limit, 100), // Amazon max limit
        OrderStatuses: [
          'Pending',
          'Unshipped',
          'PartiallyShipped',
          'Shipped',
          'Canceled',
          'Unfulfillable',
        ],
      };

      // Add date filters if provided
      if (params.startDate) {
        apiParams.CreatedAfter = params.startDate;
      }
      if (params.endDate) {
        apiParams.CreatedBefore = params.endDate;
      }

      this.logger.log('Fetching orders from Amazon API', apiParams);

      // Fetch orders from Amazon
      const ordersResponse = await this.amazonApiService.getOrders(apiParams);
      const orders = ordersResponse.orders;

      this.logger.log(`Retrieved ${orders.length} orders from Amazon`);

      // Process each order
      for (const amazonOrder of orders) {
        try {
          ordersProcessed++;

          // Check if order already exists
          const existingOrder = await this.orderRepository.findByExternalId(
            amazonOrder.orderId,
            PlatformSource.AMAZON,
          );

          if (existingOrder) {
            if (params.forceUpdate) {
              // Update existing order
              const newStatus = this.mapAmazonOrderStatus(amazonOrder.orderStatus);
              if (existingOrder.status !== newStatus) {
                existingOrder.updateStatus(newStatus);
              }

              // Update metadata
              if (!existingOrder.metadata) {
                existingOrder.metadata = {};
              }
              existingOrder.metadata.amazonData = {
                ...existingOrder.metadata.amazonData,
                lastUpdated: new Date(),
                orderStatus: amazonOrder.orderStatus,
                fulfillmentChannel: amazonOrder.fulfillmentChannel,
                salesChannel: amazonOrder.salesChannel,
              };

              await this.orderRepository.save(existingOrder);
              ordersUpdated++;
              this.logger.debug(`Updated existing Amazon order: ${amazonOrder.orderId}`);
            } else {
              // Skip existing order
              ordersSkipped++;
              this.logger.debug(`Skipped existing Amazon order: ${amazonOrder.orderId}`);
            }
          } else {
            // Create new order
            await this.processOrder(amazonOrder);
            ordersCreated++;
            this.logger.debug(`Created new Amazon order: ${amazonOrder.orderId}`);
          }
        } catch (error) {
          this.logger.error(`Failed to process Amazon order ${amazonOrder.orderId}:`, error);
          // Continue processing other orders
        }
      }

      this.logger.log(
        `Amazon order sync completed: ${ordersProcessed} processed, ${ordersCreated} created, ${ordersUpdated} updated, ${ordersSkipped} skipped`,
      );

      return {
        ordersProcessed,
        ordersCreated,
        ordersUpdated,
        ordersSkipped,
      };
    } catch (error) {
      this.logger.error('Failed to sync Amazon orders:', error);
      throw error;
    }
  }
}
