import { Controller, Get, Post, Delete, HttpCode, HttpStatus, Logger } from '@nestjs/common';
import { DesignColorCacheService } from '@shared/services/design-color-cache.service';
import { SpreadsheetConfigService } from '@shared/services/spreadsheet-config.service';

@Controller('platform/design-color-cache')
export class DesignColorCacheController {
  private readonly logger = new Logger(DesignColorCacheController.name);

  constructor(
    private readonly designColorCacheService: DesignColorCacheService,
    private readonly spreadsheetConfigService: SpreadsheetConfigService,
  ) {}

  /**
   * Get all cached design and color data
   */
  @Get()
  async getConfigData() {
    try {
      const configData = await this.designColorCacheService.getConfigData();
      return {
        success: true,
        data: configData,
        cached: !!configData,
      };
    } catch (error) {
      this.logger.error('Failed to get config data:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get cached designs
   */
  @Get('designs')
  async getDesigns() {
    try {
      const designs = await this.designColorCacheService.getDesigns();
      return {
        success: true,
        data: designs,
        count: designs.length,
      };
    } catch (error) {
      this.logger.error('Failed to get designs:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get cached colors
   */
  @Get('colors')
  async getColors() {
    try {
      const colors = await this.designColorCacheService.getColors();
      return {
        success: true,
        data: colors,
        count: colors.length,
      };
    } catch (error) {
      this.logger.error('Failed to get colors:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get cached inlay designs
   */
  @Get('inlay-designs')
  async getInlayDesigns() {
    try {
      const inlayDesigns = await this.designColorCacheService.getInlayDesigns();
      return {
        success: true,
        data: inlayDesigns,
        count: inlayDesigns.length,
      };
    } catch (error) {
      this.logger.error('Failed to get inlay designs:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get cached ring specifications
   */
  @Get('ring-specs')
  async getRingSpecs() {
    try {
      const ringSpecs = await this.designColorCacheService.getRingSpecs();
      return {
        success: true,
        data: ringSpecs,
        count: ringSpecs.length,
      };
    } catch (error) {
      this.logger.error('Failed to get ring specs:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Load and cache configuration from Google Sheets
   */
  @Post('load')
  async loadConfig() {
    try {
      this.logger.log('Loading configuration from Google Sheets');
      const configData = await this.spreadsheetConfigService.loadAndCacheConfig();

      return {
        success: true,
        data: {
          designsCount: configData.designs.length,
          colorsCount: configData.colors.length,
          inlayDesignsCount: configData.inlayDesigns.length,
          inlayGoldDesignsCount: configData.inlayGoldDesigns.length,
          inlaySilverDesignsCount: configData.inlaySilverDesigns.length,
          ringSpecsCount: configData.ringSpecs.length,
          lastUpdated: configData.lastUpdated,
        },
        message: 'Configuration loaded and cached successfully',
      };
    } catch (error) {
      this.logger.error('Failed to load configuration:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Force refresh configuration from Google Sheets
   */
  @Post('refresh')
  async refreshConfig() {
    try {
      this.logger.log('Force refreshing configuration from Google Sheets');
      const configData = await this.spreadsheetConfigService.refreshConfig();

      return {
        success: true,
        data: {
          designsCount: configData.designs.length,
          colorsCount: configData.colors.length,
          inlayDesignsCount: configData.inlayDesigns.length,
          inlayGoldDesignsCount: configData.inlayGoldDesigns.length,
          inlaySilverDesignsCount: configData.inlaySilverDesigns.length,
          ringSpecsCount: configData.ringSpecs.length,
          lastUpdated: configData.lastUpdated,
        },
        message: 'Configuration refreshed successfully',
      };
    } catch (error) {
      this.logger.error('Failed to refresh configuration:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Clear all cached configuration data
   */
  @Delete()
  @HttpCode(HttpStatus.NO_CONTENT)
  async clearCache() {
    try {
      this.logger.log('Clearing design and color cache');
      await this.designColorCacheService.clearConfigCache();

      return {
        success: true,
        message: 'Cache cleared successfully',
      };
    } catch (error) {
      this.logger.error('Failed to clear cache:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Check if configuration is cached
   */
  @Get('status')
  async getCacheStatus() {
    try {
      const isCached = await this.designColorCacheService.isConfigCached();
      const lastUpdated = await this.designColorCacheService.getLastUpdated();

      return {
        success: true,
        data: {
          isCached,
          lastUpdated,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get cache status:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Health check for Google Sheets connection
   */
  @Get('health')
  async healthCheck() {
    try {
      const isHealthy = await this.spreadsheetConfigService.healthCheck();

      return {
        success: true,
        data: {
          healthy: isHealthy,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        success: false,
        error: error.message,
        data: {
          healthy: false,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Find design by identifier
   */
  @Get('designs/find/:identifier')
  async findDesign(identifier: string) {
    try {
      const design = await this.designColorCacheService.findDesign(identifier);

      return {
        success: true,
        data: design,
        found: !!design,
      };
    } catch (error) {
      this.logger.error(`Failed to find design ${identifier}:`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Find color by identifier
   */
  @Get('colors/find/:identifier')
  async findColor(identifier: string) {
    try {
      const color = await this.designColorCacheService.findColor(identifier);

      return {
        success: true,
        data: color,
        found: !!color,
      };
    } catch (error) {
      this.logger.error(`Failed to find color ${identifier}:`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate design name
   */
  @Get('designs/validate/:designName')
  async validateDesign(designName: string) {
    try {
      const isValid = await this.designColorCacheService.validateDesign(designName);

      return {
        success: true,
        data: {
          designName,
          valid: isValid,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to validate design ${designName}:`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate color name
   */
  @Get('colors/validate/:colorName')
  async validateColor(colorName: string) {
    try {
      const isValid = await this.designColorCacheService.validateColor(colorName);

      return {
        success: true,
        data: {
          colorName,
          valid: isValid,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to validate color ${colorName}:`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}
