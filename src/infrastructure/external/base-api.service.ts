import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axiosRetry from 'axios-retry';
import { TokenManagerService } from '../../shared/services/token-manager.service';
import { TokenProvider } from '../../domain/common/entities/oauth-token.entity';

export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  headers?: Record<string, string>;
  platform?: TokenProvider;
  shopId?: string;
}

export interface RefreshTokenResult {
  accessToken: string;
  refreshToken?: string;
  expiresIn?: number;
  expiresAt?: Date;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: any;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  response?: any;
}

@Injectable()
export abstract class BaseApiService {
  protected readonly logger = new Logger(this.constructor.name);
  protected readonly httpClient: AxiosInstance;
  protected readonly config: ApiConfig;
  protected tokenManager?: TokenManagerService;
  private isRefreshingToken = false;
  private refreshPromise?: Promise<string | null>;

  constructor(config: ApiConfig, tokenManager?: TokenManagerService) {
    this.config = config;
    this.tokenManager = tokenManager;
    this.httpClient = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'knot-core-refactored/2.0.0',
        ...config.headers,
      },
    });

    // Configure retry logic
    axiosRetry(this.httpClient, {
      retries: config.retries || 3,
      retryDelay: retryCount => {
        const delay = config.retryDelay || 1000;
        return delay * Math.pow(2, retryCount - 1); // Exponential backoff
      },
      retryCondition: error => {
        return (
          axiosRetry.isNetworkOrIdempotentRequestError(error) ||
          (error.response?.status ? error.response.status >= 500 : false)
        );
      },
    });

    // Request interceptor for logging
    this.httpClient.interceptors.request.use(
      config => {
        this.logger.debug(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
        return config;
      },
      error => {
        this.logger.error('Request interceptor error:', error);
        return Promise.reject(error);
      },
    );

    // Response interceptor for logging and error handling
    this.httpClient.interceptors.response.use(
      response => {
        this.logger.debug(`Received ${response.status} response from ${response.config.url}`);
        return response;
      },
      async error => {
        this.logger.error(`API Error: ${error.message}`, {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          data: error.response?.data,
        });

        // Handle 401 errors with automatic token refresh
        if (error.response?.status === 401 && this.shouldAttemptTokenRefresh(error)) {
          return this.handleTokenRefresh(error);
        }

        return Promise.reject(this.transformError(error));
      },
    );
  }

  /**
   * Make GET request
   */
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.get<T>(url, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'GET', url);
    }
  }

  /**
   * Make POST request
   */
  public async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.post<T>(url, data, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'POST', url);
    }
  }

  /**
   * Make PUT request
   */
  public async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.put<T>(url, data, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'PUT', url);
    }
  }

  /**
   * Make PATCH request
   */
  protected async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.patch<T>(url, data, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'PATCH', url);
    }
  }

  /**
   * Make DELETE request
   */
  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.delete<T>(url, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'DELETE', url);
    }
  }

  /**
   * Transform axios response to our format
   */
  private transformResponse<T>(response: AxiosResponse<T>): ApiResponse<T> {
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    };
  }

  /**
   * Transform axios error to our format
   */
  private transformError(error: any): ApiError {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data?.message || error.message,
        status: error.response.status,
        code: error.response.data?.code || error.code,
        response: error.response.data,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        message: 'Network error - no response received',
        code: 'NETWORK_ERROR',
      };
    } else {
      // Something else happened
      return {
        message: error.message,
        code: 'UNKNOWN_ERROR',
      };
    }
  }

  /**
   * Handle and log errors
   */
  private handleError(error: any, method: string, url: string): ApiError {
    const apiError = this.transformError(error);

    this.logger.error(`${method} ${url} failed:`, {
      message: apiError.message,
      status: apiError.status,
      code: apiError.code,
    });

    return apiError;
  }

  /**
   * Set authorization header
   */
  protected setAuthHeader(token: string, type: string = 'Bearer'): void {
    this.httpClient.defaults.headers.common['Authorization'] = `${type} ${token}`;
  }

  /**
   * Remove authorization header
   */
  protected removeAuthHeader(): void {
    delete this.httpClient.defaults.headers.common['Authorization'];
  }

  /**
   * Set custom header
   */
  public setHeader(key: string, value: string): void {
    this.httpClient.defaults.headers.common[key] = value;
  }

  /**
   * Remove custom header
   */
  public removeHeader(key: string): void {
    delete this.httpClient.defaults.headers.common[key];
  }

  /**
   * Get current headers
   */
  public getHeaders(): Record<string, string> {
    return { ...this.httpClient.defaults.headers.common } as any;
  }

  /**
   * Check if we should attempt token refresh for this error
   */
  private shouldAttemptTokenRefresh(error: any): boolean {
    if (!this.tokenManager || !this.config.platform) {
      return false;
    }

    // Don't retry if this is already a retry attempt
    if (error.config?._isRetry) {
      return false;
    }

    // Check if the error indicates token expiration
    const errorMessage =
      error.response?.data?.error_description || error.response?.data?.error || '';
    const isTokenExpired =
      errorMessage.includes('expired') ||
      errorMessage.includes('invalid_token') ||
      errorMessage.includes('unauthorized');

    return isTokenExpired;
  }

  /**
   * Handle token refresh and retry the original request
   */
  private async handleTokenRefresh(originalError: any): Promise<any> {
    if (!this.tokenManager || !this.config.platform) {
      return Promise.reject(this.transformError(originalError));
    }

    // Prevent multiple simultaneous refresh attempts
    if (this.isRefreshingToken) {
      if (this.refreshPromise) {
        try {
          await this.refreshPromise;
          // Retry the original request with the new token
          return this.retryOriginalRequest(originalError.config);
        } catch (refreshError) {
          return Promise.reject(this.transformError(originalError));
        }
      }
      return Promise.reject(this.transformError(originalError));
    }

    this.isRefreshingToken = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const newAccessToken = await this.refreshPromise;
      if (newAccessToken) {
        this.logger.log(`Successfully refreshed ${this.config.platform} token`);

        // Update the authorization header
        this.updateAuthorizationHeader(newAccessToken);

        // Retry the original request
        return this.retryOriginalRequest(originalError.config);
      } else {
        this.logger.error(
          `Failed to refresh ${this.config.platform} token - service will be disabled`,
        );
        await this.handleTokenRefreshFailure();
        return Promise.reject(this.transformError(originalError));
      }
    } catch (refreshError) {
      this.logger.error(`Token refresh failed for ${this.config.platform}:`, refreshError);
      await this.handleTokenRefreshFailure();
      return Promise.reject(this.transformError(originalError));
    } finally {
      this.isRefreshingToken = false;
      this.refreshPromise = undefined;
    }
  }

  /**
   * Perform the actual token refresh using TokenManagerService
   */
  private async performTokenRefresh(): Promise<string | null> {
    if (!this.tokenManager || !this.config.platform) {
      return null;
    }

    try {
      return await this.tokenManager.getValidAccessToken(this.config.platform, this.config.shopId);
    } catch (error) {
      this.logger.error(`TokenManager failed to refresh token for ${this.config.platform}:`, error);
      return null;
    }
  }

  /**
   * Update the authorization header with the new token
   */
  private updateAuthorizationHeader(accessToken: string): void {
    if (this.config.platform === TokenProvider.ETSY) {
      this.setHeader('Authorization', `Bearer ${accessToken}`);
    } else if (this.config.platform === TokenProvider.AMAZON) {
      this.setHeader('x-amz-access-token', accessToken);
    } else if (this.config.platform === TokenProvider.SHOPIFY) {
      this.setHeader('X-Shopify-Access-Token', accessToken);
    }
  }

  /**
   * Retry the original request with the new token
   */
  private async retryOriginalRequest(originalConfig: any): Promise<any> {
    // Mark this as a retry to prevent infinite loops
    originalConfig._isRetry = true;

    try {
      const response = await this.httpClient.request(originalConfig);
      return response;
    } catch (retryError) {
      this.logger.error('Retry after token refresh failed:', retryError);
      throw retryError;
    }
  }

  /**
   * Handle token refresh failure - disable the service and prepare for alerting
   */
  private async handleTokenRefreshFailure(): Promise<void> {
    if (!this.config.platform) {
      return;
    }

    this.logger.error(`${this.config.platform} service disabled due to token refresh failure`);

    // TODO: Implement service disabling mechanism
    // TODO: Implement alerting system for token refresh failures
    // For now, we'll just log the error

    // Mark the token as failed in the database
    if (this.tokenManager) {
      try {
        // The TokenManagerService should handle marking tokens as failed
        // This will be picked up by monitoring systems
      } catch (error) {
        this.logger.error('Failed to mark token as failed:', error);
      }
    }
  }

  /**
   * Health check method - should be implemented by subclasses
   */
  abstract healthCheck(): Promise<boolean>;

  /**
   * Rate limit check - should be implemented by subclasses if needed
   */
  protected async checkRateLimit(): Promise<void> {
    // Default implementation - no rate limiting
  }
}
