import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DesignColorCacheService } from './design-color-cache.service';
import { SpreadsheetConfigService } from './spreadsheet-config.service';

@Injectable()
export class ConfigInitializationService implements OnModuleInit {
  private readonly logger = new Logger(ConfigInitializationService.name);

  constructor(
    private readonly designColorCacheService: DesignColorCacheService,
    private readonly spreadsheetConfigService: SpreadsheetConfigService,
  ) {}

  async onModuleInit() {
    try {
      this.logger.log('Initializing design and color configuration cache...');

      // Check if config is already cached
      const isCached = await this.designColorCacheService.isConfigCached();

      if (isCached) {
        this.logger.log('Configuration cache already exists, skipping initialization');
        return;
      }

      // Load and cache configuration from Google Sheets
      await this.spreadsheetConfigService.loadAndCacheConfig();

      this.logger.log('Design and color configuration cache initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize configuration cache:', error);
      // Don't throw error to prevent app startup failure
      // The cache will be loaded on-demand when needed
    }
  }

  /**
   * Manually initialize configuration cache
   */
  async initializeConfig(): Promise<boolean> {
    try {
      this.logger.log('Manually initializing configuration cache...');
      await this.spreadsheetConfigService.loadAndCacheConfig();
      this.logger.log('Configuration cache initialized successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to manually initialize configuration cache:', error);
      return false;
    }
  }

  /**
   * Check if configuration is properly initialized
   */
  async isInitialized(): Promise<boolean> {
    try {
      const isCached = await this.designColorCacheService.isConfigCached();
      if (!isCached) {
        return false;
      }

      // Check if we have essential data
      const designs = await this.designColorCacheService.getDesigns();
      const colors = await this.designColorCacheService.getColors();

      return designs.length > 0 && colors.length > 0;
    } catch (error) {
      this.logger.error('Failed to check initialization status:', error);
      return false;
    }
  }
}
