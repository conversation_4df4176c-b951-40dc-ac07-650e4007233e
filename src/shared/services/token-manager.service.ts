import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { OAuthTokenRepository } from '../../infrastructure/database/repositories/oauth-token.repository';
import { EncryptionService } from './encryption.service';
import {
  OAuthToken,
  TokenProvider,
  TokenStatus,
} from '../../domain/common/entities/oauth-token.entity';

export interface TokenData {
  accessToken: string;
  refreshToken: string;
  expiresAt?: Date;
  scopes?: string;
  shopId?: string;
  shopName?: string;
  metadata?: Record<string, any>;
}

export interface RefreshTokenFunction {
  (refreshToken: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresIn?: number;
    expiresAt?: Date;
  }>;
}

@Injectable()
export class TokenManagerService {
  private readonly logger = new Logger(TokenManagerService.name);
  private readonly refreshFunctions = new Map<TokenProvider, RefreshTokenFunction>();

  constructor(
    private readonly tokenRepository: OAuthTokenRepository,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Register a refresh function for a provider
   */
  registerRefreshFunction(provider: TokenProvider, refreshFn: RefreshTokenFunction): void {
    this.refreshFunctions.set(provider, refreshFn);
    this.logger.log(`Registered refresh function for ${provider}`);
  }

  /**
   * Store new OAuth tokens securely
   */
  async storeTokens(provider: TokenProvider, tokenData: TokenData): Promise<OAuthToken> {
    try {
      // Encrypt the tokens
      const encryptedAccessToken = this.encryptionService.encrypt(tokenData.accessToken);
      const encryptedRefreshToken = this.encryptionService.encrypt(tokenData.refreshToken);

      // Revoke existing active tokens for this provider/shop
      if (tokenData.shopId) {
        const existingToken = await this.tokenRepository.findActiveToken(
          provider,
          tokenData.shopId,
        );
        if (existingToken) {
          existingToken.markAsRevoked();
          await this.tokenRepository.save(existingToken);
        }
      }

      // Create new token record
      const token = await this.tokenRepository.create({
        provider,
        status: TokenStatus.ACTIVE,
        encryptedAccessToken,
        encryptedRefreshToken,
        expiresAt: tokenData.expiresAt,
        scopes: tokenData.scopes,
        shopId: tokenData.shopId,
        shopName: tokenData.shopName,
        metadata: tokenData.metadata,
        lastUsedAt: new Date(),
        lastRefreshedAt: new Date(),
        refreshFailureCount: 0,
      });

      this.logger.log(`Stored new ${provider} token for shop ${tokenData.shopId || 'default'}`);
      return token;
    } catch (error) {
      this.logger.error(`Failed to store ${provider} tokens:`, error);
      throw new Error(`Failed to store ${provider} tokens`);
    }
  }

  /**
   * Get valid access token (refresh if needed)
   */
  async getValidAccessToken(provider: TokenProvider, shopId?: string): Promise<string | null> {
    try {
      const token = await this.tokenRepository.findActiveToken(provider, shopId);
      if (!token) {
        this.logger.warn(`No active token found for ${provider}${shopId ? ` shop ${shopId}` : ''}`);
        return null;
      }

      // Check if token needs refresh or has been failing
      if (token.needsRefresh() || token.refreshFailureCount > 0) {
        this.logger.log(
          `Token for ${provider} needs refresh (needsRefresh: ${token.needsRefresh()}, failureCount: ${token.refreshFailureCount}), attempting refresh...`,
        );
        const refreshed = await this.refreshToken(token);
        if (!refreshed) {
          return null;
        }
        return this.encryptionService.decrypt(refreshed.encryptedAccessToken);
      }

      // Mark as used and return decrypted token
      token.markAsUsed();
      await this.tokenRepository.save(token);

      return this.encryptionService.decrypt(token.encryptedAccessToken);
    } catch (error) {
      this.logger.error(`Failed to get valid access token for ${provider}:`, error);
      return null;
    }
  }

  /**
   * Refresh a specific token
   */
  async refreshToken(token: OAuthToken): Promise<OAuthToken | null> {
    const refreshFn = this.refreshFunctions.get(token.provider);
    if (!refreshFn) {
      this.logger.error(`No refresh function registered for ${token.provider}`);
      return null;
    }

    try {
      // Decrypt the refresh token
      const refreshToken = this.encryptionService.decrypt(token.encryptedRefreshToken);

      // Call the provider-specific refresh function
      const refreshResult = await refreshFn(refreshToken);

      // Update the token with new values
      token.encryptedAccessToken = this.encryptionService.encrypt(refreshResult.accessToken);

      if (refreshResult.refreshToken) {
        token.encryptedRefreshToken = this.encryptionService.encrypt(refreshResult.refreshToken);
      }

      if (refreshResult.expiresAt) {
        token.expiresAt = refreshResult.expiresAt;
      } else if (refreshResult.expiresIn) {
        token.expiresAt = new Date(Date.now() + refreshResult.expiresIn * 1000);
      }

      token.markAsRefreshed();
      await this.tokenRepository.save(token);

      this.logger.log(`Successfully refreshed ${token.provider} token`);
      return token;
    } catch (error) {
      this.logger.error(`Failed to refresh ${token.provider} token:`, error);

      token.markRefreshFailed(error.message);
      await this.tokenRepository.save(token);

      return null;
    }
  }

  /**
   * Revoke tokens for a provider
   */
  async revokeTokens(provider: TokenProvider, shopId?: string): Promise<void> {
    try {
      if (shopId) {
        const token = await this.tokenRepository.findActiveToken(provider, shopId);
        if (token) {
          token.markAsRevoked();
          await this.tokenRepository.save(token);
        }
      } else {
        await this.tokenRepository.revokeAllForProvider(provider);
      }

      this.logger.log(`Revoked ${provider} tokens${shopId ? ` for shop ${shopId}` : ''}`);
    } catch (error) {
      this.logger.error(`Failed to revoke ${provider} tokens:`, error);
      throw error;
    }
  }

  /**
   * Get token status for a provider
   */
  async getTokenStatus(
    provider: TokenProvider,
    shopId?: string,
  ): Promise<{
    hasToken: boolean;
    isValid: boolean;
    expiresAt?: Date;
    lastUsedAt?: Date;
    refreshFailureCount: number;
  }> {
    const token = await this.tokenRepository.findActiveToken(provider, shopId);

    if (!token) {
      return {
        hasToken: false,
        isValid: false,
        refreshFailureCount: 0,
      };
    }

    return {
      hasToken: true,
      isValid: token.isActive(),
      expiresAt: token.expiresAt,
      lastUsedAt: token.lastUsedAt,
      refreshFailureCount: token.refreshFailureCount,
    };
  }

  /**
   * Automatic token refresh job (runs every 5 minutes)
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async autoRefreshTokens(): Promise<void> {
    try {
      const tokensNeedingRefresh = await this.tokenRepository.findTokensNeedingRefresh();

      if (tokensNeedingRefresh.length === 0) {
        return;
      }

      this.logger.log(`Found ${tokensNeedingRefresh.length} tokens needing refresh`);

      for (const token of tokensNeedingRefresh) {
        await this.refreshToken(token);
      }
    } catch (error) {
      this.logger.error('Auto refresh tokens job failed:', error);
    }
  }

  /**
   * Cleanup expired tokens job (runs daily)
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const expiredTokens = await this.tokenRepository.findExpiredTokens();

      for (const token of expiredTokens) {
        token.markAsExpired();
        await this.tokenRepository.save(token);
      }

      // Clean up old soft-deleted tokens
      const deletedCount = await this.tokenRepository.cleanupOldTokens(90);

      this.logger.log(
        `Marked ${expiredTokens.length} tokens as expired, cleaned up ${deletedCount} old tokens`,
      );
    } catch (error) {
      this.logger.error('Cleanup expired tokens job failed:', error);
    }
  }

  /**
   * Mark token as failed to trigger refresh
   */
  async markTokenAsFailed(provider: TokenProvider, shopId?: string, error?: string): Promise<void> {
    try {
      const token = await this.tokenRepository.findActiveToken(provider, shopId);
      if (token) {
        token.markRefreshFailed(error || 'Unknown error');
        await this.tokenRepository.save(token);
        this.logger.log(`Marked ${provider} token as failed: ${error}`);
      }
    } catch (error) {
      this.logger.error(`Failed to mark ${provider} token as failed:`, error);
    }
  }

  /**
   * Get token statistics
   */
  async getTokenStatistics(): Promise<any> {
    return this.tokenRepository.getTokenStats();
  }
}
