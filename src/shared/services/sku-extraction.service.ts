import { Injectable } from '@nestjs/common';

export interface SkuParseResult {
  style?: string;
  color?: string;
  size?: string;
  design?: string;
  inlayType?: 'gold' | 'silver' | 'clear' | 'none';
}

export interface OrderData {
  style?: string;
  color?: string;
  size?: string;
  design?: string;
  sku: string;
  title?: string;
  source: 'shopify' | 'etsy';
}

@Injectable()
export class SkuExtractionService {
  // Color variant mappings - maps abbreviations and variants to canonical color names
  private readonly colorVariantMappings: Record<string, string> = {
    // Rose Gold variants
    rgold: 'RoseGold',
    roseg: 'RoseGold',

    champ: 'Champagne',

    // Northern Lights variants
    nl: 'NLights',
    northernlight: 'NLights',
    northernlights: 'NLights',

    // Enchanted variants
    ef: 'Enchanted',
    ench: 'Enchanted',

    // Other common variants
    dsilv: 'DSilver',
    darksilv: 'DSilver',
    lgold: 'LGold',
    lightgold: 'LGold',
    lgrey: 'LGrey',
    lightgrey: 'L<PERSON>rey',
    bblue: 'BBlue',
    babyblue: 'BabyBlue',
    bmarble: 'BMarble',
    blackmarble: 'BMarble',
    wmarble: 'WMarble',
    whitemarble: 'WMarble',
    pwhite: 'PWhite',
    purewhite: 'PWhite',
    ppink: 'PPink',
    bpink: 'BPink',
    neonp: 'NeonPink',
    neonl: 'NeonLemon',
    neono: 'NeonOrange',
    neons: 'NeonSwirl',
    dbronze: 'DarkBronze',
    darkbronze: 'DarkBronze',
    metalteal: 'MetalTeal',
    metearthgreen: 'MetEarthGreen',
    starpurple: 'StarPurple',
    starfrost: 'StarFrost',
    aquablue: 'AquaBlue',
    bluesky: 'BlueSky',
  };

  // Design variant mappings - maps abbreviations and variants to canonical design names
  private readonly designVariantMappings: Record<string, string> = {
    // Filigree variants
    silverfiligree: 'Fili',
    goldfiligree: 'Fili',
    filigree: 'Fili',
    fili: 'Fili',

    // Trinity variants
    trinity: 'TrinityZ',
    trinityz: 'TrinityZ',

    // Floral variants
    floral: 'Floral',
    goldfloral: 'GoldFloral',

    // Celtic variants
    celtic: 'Celtic',
    celticinf: 'CelticInfinity',
    celticinfinity: 'CelticInfinity',
    celticsailor: 'CelticSailor',
    celticbutterfly: 'CelticButterfly',
    celticweave: 'Celtic-Weave',
    celticfig8: 'Celtic-Fig8',

    // Warrior variants
    warrior: 'StrengthWarrior', // Default to StrengthWarrior
    strengthwarrior: 'StrengthWarrior',
    couragewarrior: 'CourageWarrior',
    serenitywarrior: 'SerenityWarrior',
    wisdomwarrior: 'WisdomWarrior',

    // Henna variants
    henna: 'HennaCrown', // Default to HennaCrown
    hennacrown: 'HennaCrown',
    hennaheart: 'HennaHeart',
    hennaroot: 'HennaRoot',

    // Heart variants
    heart: 'HeartV',
    heartv: 'HeartV',
    goldheartv: 'GoldHeartV',

    // Laurel variants
    laurel: 'Laurel',
    goldlaurel: 'GoldLaurel',

    // Tribal variants
    tribal: 'Tribal-TikiFrogEyes', // Default to first tribal
    tikifrogeyes: 'Tribal-TikiFrogEyes',
    sharkspears: 'Tribal-SharkSpears',
    doublewaves: 'Tribal-DoubleWaves',

    // Rune variants
    rune: 'RuneRustic',
    runerustic: 'RuneRustic',
    runeiluvu: 'Rune_iLuvU',
    runeeternallluv: 'Rune_eternalLuv',
    runealwaysurs: 'Rune_alwaysUrs',
    runeilu: 'Rune_ILU',
    runemyluv: 'Rune_MyLuv',
    runeours: 'Rune_Urs',

    // Love variants
    love: 'Luv_iLuvU', // Default to first love design
    iluvu: 'Luv_iLuvU',
    jetaime: 'Luv_Jetaime',
    luved: 'Luv_Luved',
    luvcap: 'Luv_Cap',
    luvhearts: 'Luv_Hearts',
    luvsolidhearts: 'Luv_solidHearts',
    luvdrawnhearts: 'Luv_drawnHearts',

    // Animal variants
    cat: 'Cats',
    cats: 'Cats',
    catpaws: 'CatPaws',
    dog: 'DogPaws',
    dogpaws: 'DogPaws',
    bear: 'bearForest',
    bearforest: 'bearForest',
    bearpaws: 'bearPaws',
    horse: 'horse_Graze',
    horsegraze: 'horse_Graze',
    horserun: 'horse_Run',
    horseshoe: 'horse_Shoe',
    elephant: 'Elephants',
    elephants: 'Elephants',
    bunny: 'Bunnies',
    bunnies: 'Bunnies',
    unicorn: 'Unicorn',

    // Zodiac variants (short forms)
    aquarius: '1-Aquarius',
    pisces: '2-Pisces',
    aries: '3-Aries',
    taurus: '4-Taurus',
    gemini: '5-Gemini',
    cancer: '6-Cancer',
    leo: '7-Leo',
    virgo: '8-Virgo',
    libra: '9-Libra',
    scorpio: '10-Scorpio',
    sag: '11-Sag',
    sagittarius: '11-Sag',
    capricorn: '12-Capricorn',
    cap: '12-Capricorn',

    // Other common variants
    clover: 'goldClovers',
    clovers: 'goldClovers',
    goldclovers: 'goldClovers',
    wolf: 'WolfMoon',
    wolfmoon: 'WolfMoon',
    dragon: 'Dragon',
    snake: 'Snake',
    mushroom: 'Mushroom',
    bike: 'Bike',
    sparta: 'Sparta',
    molanlabe: 'MolanLabe',
    naughty: 'Naughty',
    eyehorus: 'EyeHorus',
    horus: 'EyeHorus',
    bouquet: 'Bouquet',
    nordicbloom: 'NordicBloom',
    grapevine: 'GrapeVine2',
    lovebirds: 'LoveBirds',
    bird: 'LoveBirds',
    birds: 'LoveBirds',
  };

  // Mock SKU valid parameters - in a real implementation, this would come from a config service
  private readonly skuValidParams = [
    // Colors
    { value: 'Amethyst', type: 'color' },
    { value: 'Gold', type: 'color' },
    { value: 'Tan', type: 'color' },
    { value: 'AquaBlue', type: 'color' },
    { value: 'Champagne', type: 'color' },
    { value: 'BabyBlue', type: 'color' },
    { value: 'BBlue', type: 'color' },
    { value: 'Black', type: 'color' },
    { value: 'BMarble', type: 'color' },
    { value: 'BlueSky', type: 'color' },
    { value: 'Blueberry', type: 'color' },
    { value: 'Champagne', type: 'color' },
    { value: 'Copper', type: 'color' },
    { value: 'Cosmic', type: 'color' },
    { value: 'DBronze', type: 'color' },
    { value: 'DSilver', type: 'color' },
    { value: 'MetEarthGreen', type: 'color' },
    { value: 'Emerald', type: 'color' },
    { value: 'StarPurple', type: 'color' },
    { value: 'Enchanted', type: 'color' },
    { value: 'Fuchsia', type: 'color' },
    { value: 'Heather', type: 'color' },
    { value: 'Lavender', type: 'color' },
    { value: 'LGold', type: 'color' },
    { value: 'LGrey', type: 'color' },
    { value: 'Lilac', type: 'color' },
    { value: 'Blue', type: 'color' },
    { value: 'Red', type: 'color' },
    { value: 'MetalTeal', type: 'color' },
    { value: 'NLight', type: 'color' },
    { value: 'Olive', type: 'color' },
    { value: 'Orange', type: 'color' },
    { value: 'Other', type: 'color' },
    { value: 'PWhite', type: 'color' },
    { value: 'WhiteRGold', type: 'color' },
    { value: 'Rainbow', type: 'color' },
    { value: 'RoseGold', type: 'color' },
    { value: 'Silver', type: 'color' },
    { value: 'StarFrost', type: 'color' },
    { value: 'Teal', type: 'color' },
    { value: 'Turquoise', type: 'color' },
    { value: 'White', type: 'color' },
    { value: 'WMarble', type: 'color' },
    { value: 'Khaki', type: 'color' },
    { value: 'PPink', type: 'color' },
    { value: 'BPink', type: 'color' },
    { value: 'NeonPink', type: 'color' },
    { value: 'NeonLemon', type: 'color' },
    { value: 'NeonOrange', type: 'color' },
    { value: 'NeonSwirl', type: 'color' },
    { value: 'NLights', type: 'color' },
    { value: 'DarkBronze', type: 'color' },

    // Styles
    { value: 'CF4', type: 'style' },
    { value: 'CF6', type: 'style' },
    { value: 'SCF', type: 'style' },
    { value: 'BCF6', type: 'style' },
    { value: 'D6', type: 'style' },
    { value: 'Pyra', type: 'style' },
    { value: 'BevelCF', type: 'style' },

    // Sizes
    { value: 'S04', type: 'size' },
    { value: 'S05', type: 'size' },
    { value: 'S06', type: 'size' },
    { value: 'S07', type: 'size' },
    { value: 'S08', type: 'size' },
    { value: 'S09', type: 'size' },
    { value: 'S10', type: 'size' },
    { value: 'S11', type: 'size' },
    { value: 'S12', type: 'size' },
    { value: 'S13', type: 'size' },
    { value: 'S14', type: 'size' },
    { value: 'S15', type: 'size' },
    { value: 'S16', type: 'size' },

    // Designs
    { value: 'Breathe', type: 'design' },
    { value: 'CatPaws', type: 'design' },
    { value: 'Celtic', type: 'design' },
    { value: 'Claddagh', type: 'design' },
    { value: 'DogPaws', type: 'design' },
    { value: 'Forest', type: 'design' },
    { value: 'Greek', type: 'design' },
    { value: 'LOTR', type: 'design' },
    { value: 'Lotus', type: 'design' },
    { value: 'LotusOm', type: 'design' },
    { value: 'Mount', type: 'design' },
    { value: 'Om', type: 'design' },
    { value: 'RideOrDie', type: 'design' },
    { value: 'Skeleton', type: 'design' },
    { value: 'Skulls', type: 'design' },
    { value: 'Snowflakes', type: 'design' },
    { value: 'TrinityZ', type: 'design' },
    { value: 'Unalome', type: 'design' },
    { value: 'Waves', type: 'design' },
    { value: 'SF-Sky', type: 'design' },
    { value: 'Cats', type: 'design' },
    { value: 'Elephants', type: 'design' },
    { value: 'Safari', type: 'design' },
    { value: 'Bunnies', type: 'design' },
    { value: 'Unicorn', type: 'design' },
    { value: 'TrinityChubby', type: 'design' },
    { value: 'HennaCrown', type: 'design' },
    { value: 'HennaHeart', type: 'design' },
    { value: 'HennaRoot', type: 'design' },
    { value: 'GoldFili', type: 'design' },
    { value: 'GoldFloral', type: 'design' },
    { value: 'GoldHeartV', type: 'design' },
    { value: 'GoldLaurel', type: 'design' },
    { value: 'Lace', type: 'design' },
    { value: 'Fili', type: 'design' },
    { value: 'Floral', type: 'design' },
    { value: 'HeartV', type: 'design' },
    { value: 'Laurel', type: 'design' },
    { value: 'Tribal-TikiFrogEyes', type: 'design' },
    { value: 'Tribal-SharkSpears', type: 'design' },
    { value: 'Tribal-DoubleWaves', type: 'design' },
    { value: 'WisdomWarrior', type: 'design' },
    { value: 'CourageWarrior', type: 'design' },
    { value: 'SerenityWarrior', type: 'design' },
    { value: 'StrengthWarrior', type: 'design' },
    { value: 'WinterVillage', type: 'design' },
    { value: 'XmasIcons', type: 'design' },
    { value: 'SantaReindeer', type: 'design' },
    { value: 'RuneRustic', type: 'design' },
    { value: 'Dragon', type: 'design' },
    { value: 'Snowboarders', type: 'design' },
    { value: 'Skiers', type: 'design' },
    { value: 'Cancer', type: 'design' },
    { value: 'Virgo', type: 'design' },
    { value: 'Aries', type: 'design' },
    { value: 'Flower01', type: 'design' },
    { value: 'Flower02', type: 'design' },
    { value: 'Flower03', type: 'design' },
    { value: 'Flower04', type: 'design' },
    { value: 'Flower05', type: 'design' },
    { value: 'Flower06', type: 'design' },
    { value: 'Flower07', type: 'design' },
    { value: 'Flower08', type: 'design' },
    { value: 'Flower09', type: 'design' },
    { value: 'Flower10', type: 'design' },
    { value: 'Flower11', type: 'design' },
    { value: 'Flower12', type: 'design' },
    { value: '1-Aquarius', type: 'design' },
    { value: '2-Pisces', type: 'design' },
    { value: '3-Aries', type: 'design' },
    { value: '4-Taurus', type: 'design' },
    { value: '5-Gemini', type: 'design' },
    { value: '6-Cancer', type: 'design' },
    { value: '7-Leo', type: 'design' },
    { value: '8-Virgo', type: 'design' },
    { value: '9-Libra', type: 'design' },
    { value: '10-Scorpio', type: 'design' },
    { value: '11-Sag', type: 'design' },
    { value: '12-Capricorn', type: 'design' },
    { value: 'Triquetra', type: 'design' },
    { value: 'Celtic-Weave', type: 'design' },
    { value: 'Celtic-Fig8', type: 'design' },
    { value: 'CelticInfinity', type: 'design' },
    { value: 'CelticButterfly', type: 'design' },
    { value: 'CelticSailor', type: 'design' },
    { value: 'bearForest', type: 'design' },
    { value: 'bearPaws', type: 'design' },
    { value: 'horse_Graze', type: 'design' },
    { value: 'horse_Run', type: 'design' },
    { value: 'horse_Shoe', type: 'design' },
    { value: 'Naughty', type: 'design' },
    { value: 'EyeHorus', type: 'design' },
    { value: 'Luv_iLuvU', type: 'design' },
    { value: 'Luv_Jetaime', type: 'design' },
    { value: 'Luv_Luved', type: 'design' },
    { value: 'Luv_Cap', type: 'design' },
    { value: 'Luv_Hearts', type: 'design' },
    { value: 'Luv_solidHearts', type: 'design' },
    { value: 'Luv_drawnHearts', type: 'design' },
    { value: 'Snake', type: 'design' },
    { value: 'Rune_iLuvU', type: 'design' },
    { value: 'Rune_eternalLuv', type: 'design' },
    { value: 'Rune_alwaysUrs', type: 'design' },
    { value: 'Rune_ILU', type: 'design' },
    { value: 'Rune_MyLuv', type: 'design' },
    { value: 'Rune_Urs', type: 'design' },
    { value: 'goldClovers', type: 'design' },
    { value: 'Bike', type: 'design' },
    { value: 'Sparta', type: 'design' },
    { value: 'MolanLabe', type: 'design' },
    { value: 'Mushroom', type: 'design' },
    { value: 'WolfMoon', type: 'design' },
    { value: 'Bouquet', type: 'design' },
    { value: 'NordicBloom', type: 'design' },
    { value: 'GrapeVine2', type: 'design' },
    { value: 'LoveBirds', type: 'design' },
  ];

  /**
   * Extract color from SKU text using length-based matching and variant mappings
   * Prioritizes longer color names to avoid partial matches (e.g., RoseGold before Gold)
   */
  private extractColorFromText(text: string): string | undefined {
    const textLower = text.toLowerCase();

    // Get all color variant mappings and sort by length (longest first) for better matching
    const sortedVariants = Object.entries(this.colorVariantMappings).sort(
      ([a], [b]) => b.length - a.length,
    );

    // First, check for variant mappings with more precise matching
    for (const [variant, canonical] of sortedVariants) {
      const variantLower = variant.toLowerCase();

      // For short variants like "nl", "ef", use word boundary or exact segment matching
      if (variantLower.length <= 2) {
        // Check if it's a standalone segment (surrounded by delimiters or at start/end)
        const pattern = new RegExp(`(^|[-_\\s])${variantLower}([-_\\s]|$)`, 'i');
        if (pattern.test(textLower)) {
          return canonical;
        }
      } else {
        // For longer variants, use simple includes
        if (textLower.includes(variantLower)) {
          return canonical;
        }
      }
    }

    // Get all valid colors and sort by length (longest first)
    const validColors = this.skuValidParams
      .filter(param => param.type === 'color')
      .sort((a, b) => b.value.length - a.value.length);

    // Look for color matches, prioritizing longer names
    for (const color of validColors) {
      const colorLower = color.value.toLowerCase();
      if (textLower.includes(colorLower)) {
        return color.value;
      }
    }

    return undefined;
  }

  /**
   * Extract design from SKU text using length-based matching and variant mappings
   * Prioritizes longer design names to avoid partial matches and handles common abbreviations
   */
  private extractDesignFromText(text: string): string | undefined {
    const textLower = text.toLowerCase();

    // Get all design variant mappings and sort by length (longest first) for better matching
    const sortedVariants = Object.entries(this.designVariantMappings).sort(
      ([a], [b]) => b.length - a.length,
    );

    // First, check for design variant mappings with more precise matching
    for (const [variant, canonical] of sortedVariants) {
      const variantLower = variant.toLowerCase();

      // For short variants, use word boundary or exact segment matching
      if (variantLower.length <= 3) {
        // Check if it's a standalone segment (surrounded by delimiters or at start/end)
        const pattern = new RegExp(`(^|[-_\\s])${variantLower}([-_\\s]|$)`, 'i');
        if (pattern.test(textLower)) {
          return canonical;
        }
      } else {
        // For longer variants, use simple includes
        if (textLower.includes(variantLower)) {
          return canonical;
        }
      }
    }

    // Get all valid designs and sort by length (longest first)
    const validDesigns = this.skuValidParams
      .filter(param => param.type === 'design')
      .sort((a, b) => b.value.length - a.value.length);

    // Look for design matches, prioritizing longer names
    for (const design of validDesigns) {
      const designLower = design.value.toLowerCase();

      // For very short design names (2 characters or less), use word boundary matching
      if (designLower.length <= 2) {
        const pattern = new RegExp(`(^|[-_\\s])${designLower}([-_\\s]|$)`, 'i');
        if (pattern.test(textLower)) {
          return design.value;
        }
      } else {
        // For longer design names, use simple includes
        if (textLower.includes(designLower)) {
          return design.value;
        }
      }
    }

    return undefined;
  }

  /**
   * Extract inlay type from design prefix or SKU patterns
   */
  private extractInlayType(
    sku: string,
    design?: string,
    color?: string,
  ): 'gold' | 'silver' | 'clear' | 'none' | undefined {
    const skuLower = sku.toLowerCase();

    // Check for new inlay patterns
    if (skuLower.includes('-gs') || skuLower.includes('gs') || design?.startsWith('gs')) {
      return 'gold';
    }
    if (skuLower.includes('-ss') || skuLower.includes('ss') || design?.startsWith('ss')) {
      return 'silver';
    }
    if (
      skuLower.includes('-cs') ||
      skuLower.includes('cs') ||
      design?.startsWith('cs') ||
      skuLower.includes('csclad')
    ) {
      return 'clear';
    }

    // Legacy patterns
    if (skuLower.includes('goldink')) {
      return 'gold';
    }
    if (skuLower.includes('silverink')) {
      return 'silver';
    }

    if (skuLower.includes('silverfiligree')) {
      return 'silver';
    }

    return undefined;
  }

  /**
   * Parse SKU to extract style, color, size, and design information
   *
   * Supports design codes with prefixes:
   * - csfloral -> floral (strips 'cs' prefix) - cs = clear silicone (no inlay)
   * - gsfloral -> floral (strips 'gs' prefix) - gs = gold silicone (gold inlay)
   * - ssfloral -> floral (strips 'ss' prefix) - ss = silver silicone (silver inlay)
   *
   * New inlay patterns:
   * - -gsFili-, -ssFili-, -csFili- (gold, silver, clear inlay with design)
   * - csClad (clear silicone with no color inlay)
   *
   * Style pattern updates:
   * - CF4 and CF-4mm -> C4
   *
   * Example SKU: G-CF6-S11-csfloral-GOLD
   * - style: CF6
   * - size: S11
   * - design: csfloral (stripped to 'floral' for lookup, but keeps original in result)
   * - color: GOLD
   */
  parseSku(sku: string): SkuParseResult {
    const parsed: SkuParseResult = {};

    // Handle both G- prefixed SKUs and descriptive SKUs
    if (!sku || (!sku.match(/^G-/) && !sku.includes('-'))) return {};

    const parts = sku.split('-');

    parts.forEach(value => {
      // Handle design codes with prefixes (cs, gs, ss)
      let processedValue = value;
      let foundParam = this.skuValidParams.find(p => p.value === processedValue);

      // If not found and it might be a design with prefix, try stripping prefixes
      if (
        !foundParam &&
        (value.startsWith('cs') || value.startsWith('gs') || value.startsWith('ss'))
      ) {
        // Strip the prefix (cs, gs, ss) and try again
        processedValue = value.substring(2); // Remove first 2 characters
        foundParam = this.skuValidParams.find(p => p.value === processedValue);

        // If found with stripped prefix, use the original value for the parsed result
        // but mark it as a design type
        if (foundParam && foundParam.type === 'design') {
          parsed.design = value; // Keep original value with prefix
          return;
        }

        // If still not found, try case-insensitive matching
        if (!foundParam) {
          foundParam = this.skuValidParams.find(
            p => p.type === 'design' && p.value.toLowerCase() === processedValue.toLowerCase(),
          );
          if (foundParam) {
            parsed.design = value; // Keep original value with prefix
            return;
          }
        }
      }

      // If found with original value, use it
      if (foundParam) {
        const paramType = foundParam.type as keyof SkuParseResult;
        if (paramType !== 'inlayType') {
          parsed[paramType] = value;
        }
      }
    });

    // If no color was found through standard parameter matching, try enhanced color extraction
    if (!parsed.color) {
      parsed.color = this.extractColorFromText(sku);
    }

    // If no design was found through standard parameter matching, try enhanced design extraction
    if (!parsed.design) {
      parsed.design = this.extractDesignFromText(sku);
    }

    // Extract inlay type from the SKU and design
    parsed.inlayType = this.extractInlayType(sku, parsed.design, parsed.color);

    return parsed;
  }

  /**
   * Enhanced SKU extractor that fills in missing order data fields
   * Uses SKU parsing as fallback when style, color, design, or size are missing
   */
  extractCompleteOrderData(orderData: OrderData): SkuParseResult {
    const { style, color, size: orderSize, design, sku, title } = orderData;

    // Start with existing data and format size if needed
    let formattedSize = orderSize;

    // Format existing size if it's just a number (e.g., "5" -> "S05")
    if (formattedSize && /^\d{1,2}(\.\d)?$/.test(formattedSize)) {
      const sizeNum = formattedSize.padStart(2, '0');
      formattedSize = `S${sizeNum}`;
    }

    const result: SkuParseResult = {
      style: style || undefined,
      color: color || undefined,
      size: formattedSize || undefined,
      design: design || undefined,
      inlayType: undefined,
    };

    // Parse SKU to get fallback values
    const skuParsed = this.parseSku(sku);

    // Fill in missing fields from SKU parsing
    if (!result.style && skuParsed.style) {
      result.style = skuParsed.style;
    }

    if (!result.color && skuParsed.color) {
      result.color = skuParsed.color;
    }

    if (!result.size && skuParsed.size) {
      result.size = skuParsed.size;
    }

    if (!result.design && skuParsed.design) {
      result.design = skuParsed.design;
    }

    // Always extract inlay type from SKU parsing
    if (skuParsed.inlayType) {
      result.inlayType = skuParsed.inlayType;
    }

    // Enhanced style extraction for descriptive SKU patterns
    if (!result.style && sku) {
      const skuLower = sku.toLowerCase();
      let inferredStyle: string | undefined;

      // Handle descriptive SKU patterns like "ComfortFit-6mm-GreenEnchanted-12" and "CF-4mm-StarPurple-11"
      if (
        skuLower.includes('bigcomfortfit') ||
        (skuLower.includes('big') && skuLower.includes('comfortfit'))
      ) {
        inferredStyle = 'BCF6';
      } else if (skuLower.includes('comfortfit') && skuLower.includes('6mm')) {
        inferredStyle = 'CF6';
      } else if (skuLower.includes('comfortfit') && skuLower.includes('4mm')) {
        inferredStyle = 'CF4';
      } else if (
        (skuLower.startsWith('cf-') || skuLower.includes('-cf-')) &&
        skuLower.includes('4mm')
      ) {
        inferredStyle = 'CF4';
      } else if (
        (skuLower.startsWith('cf-') || skuLower.includes('-cf-')) &&
        skuLower.includes('6mm')
      ) {
        inferredStyle = 'CF6';
      } else if (skuLower.includes('domed') && skuLower.includes('6mm')) {
        inferredStyle = 'D6';
      } else if (skuLower.includes('stepped') && skuLower.includes('comfort')) {
        inferredStyle = 'SCF';
      }
      // Legacy patterns
      else if (skuLower.includes('d6-')) {
        inferredStyle = 'D6';
      } else if (skuLower.includes('scf-')) {
        inferredStyle = 'SCF';
      } else if (skuLower.includes('bcf6-') || skuLower.includes('bigcf6')) {
        inferredStyle = 'BCF6';
      } else if (skuLower.includes('cf6-') || skuLower.includes('c6')) {
        inferredStyle = 'CF6';
      } else if (skuLower.includes('cf4-') || skuLower.includes('c4')) {
        inferredStyle = 'CF4';
      }

      if (inferredStyle) {
        result.style = inferredStyle;
      }
    }

    // Enhanced size extraction from descriptive SKUs
    if (!result.size && sku) {
      // Look for size patterns in SKU like "ComfortFit-6mm-GreenEnchanted-12" or "ComfortFit-6mm-Black-R11"
      let sizeMatch = sku.match(/[-_]R(\d{1,2}(?:\.\d)?)(?:$|[-_])/i); // R11, R8, etc.
      if (!sizeMatch) {
        sizeMatch = sku.match(/[-_](\d{1,2}(?:\.\d)?)(?:$|[-_])/); // 12, 8, etc.
      }
      if (sizeMatch) {
        const sizeNum = sizeMatch[1].padStart(2, '0');
        result.size = `S${sizeNum}`;
      }
    }

    // Enhanced color extraction using length-based matching and variant mappings
    if (!result.color && sku) {
      result.color = this.extractColorFromText(sku);
    }

    // Enhanced design extraction using length-based matching and variant mappings
    if (!result.design && sku) {
      result.design = this.extractDesignFromText(sku);
    }

    // Try to extract additional info from title if available
    if (title) {
      // Extract size from title if missing (common patterns)
      if (!result.size) {
        const sizeMatch = title.match(/\b(size\s+)?(\d{1,2}(?:\.\d)?)\b/i);
        if (sizeMatch) {
          const sizeNum = sizeMatch[2].padStart(2, '0');
          result.size = `S${sizeNum}`;
        }
      }

      // Extract color from title if missing using enhanced color extraction
      if (!result.color) {
        result.color = this.extractColorFromText(title);
      }
    }

    return result;
  }

  /**
   * Extract engraving and customization data from Etsy variations
   */
  extractEngravingData(variations: any[]): {
    personalization?: string;
    engravingSide?: string;
    inkColor?: string;
    outside: Record<string, any>;
    inside: Record<string, any>;
    isEngraved: boolean;
  } {
    let personalization: string | undefined;
    let engravingSide: string | undefined;
    let inkColor: string | undefined;
    const customFields: { label: string; value: any }[] = [];

    // Process variations to extract engraving data
    variations.forEach(({ property_id, formatted_name, formatted_value }) => {
      const decodedName = formatted_name || '';
      const decodedValue = formatted_value || '';

      customFields.push({
        label: decodedName,
        value: decodedValue,
      });

      if (property_id === 54) {
        // 54 = personalization
        personalization = decodedValue;
        if (personalization === 'Not requested on this item.') {
          personalization = undefined;
        }
      }
      if (property_id === 513) {
        // 513 = engraving side
        engravingSide = decodedValue;
      }
      if (property_id === 514 && formatted_value === 'Yes') {
        // 514 = Inside Engraving?
        engravingSide = 'inside';
      }

      // Check for ink-color property
      if (
        decodedName.toLowerCase().includes('ink-color') ||
        decodedName.toLowerCase().includes('ink color')
      ) {
        inkColor = decodedValue.toLowerCase();
      }
    });

    // Build outside and inside objects
    let outside = {};
    let inside = {};

    if (engravingSide) {
      const engravingSideLowercase = engravingSide.toLowerCase();

      if (
        engravingSideLowercase.includes('inside') &&
        !engravingSideLowercase.includes('outside')
      ) {
        inside = { text: personalization };
      } else if (
        engravingSideLowercase.includes('outside') &&
        !engravingSideLowercase.includes('inside')
      ) {
        outside = { text: personalization };
      } else if (
        engravingSideLowercase.includes('outside') &&
        engravingSideLowercase.includes('inside')
      ) {
        outside = { text: '?' };
        inside = { text: '?' };
      }
    }

    const isEngraved = !!(
      personalization ||
      (typeof outside === 'object' && Object.keys(outside).length > 0) ||
      (typeof inside === 'object' && Object.keys(inside).length > 0)
    );

    return {
      personalization,
      engravingSide,
      inkColor,
      outside,
      inside,
      isEngraved,
    };
  }
}
